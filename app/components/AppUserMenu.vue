<script setup lang="ts">
import type { DropdownMenuItem } from '@nuxt/ui';

const { signOut, data } = useAuth();
const { theme, setTheme } = useTheme();

const userName = computed(() => data.value?.user?.name);
const userImage = computed(() => data.value?.user?.image);
const userEmail = computed(() => data.value?.user?.email);

const getThemeIcon = (currentTheme: string) => {
    switch (currentTheme) {
        case 'light':
            return 'i-lucide-sun';
        case 'dark':
            return 'i-lucide-moon';
        case 'system':
        default:
            return 'i-lucide-monitor';
    }
};

const getThemeLabel = (currentTheme: string) => {
    switch (currentTheme) {
        case 'light':
            return 'Light Theme';
        case 'dark':
            return 'Dark Theme';
        case 'system':
        default:
            return 'System Theme';
    }
};

const cycleTheme = () => {
    const themes = ['system', 'light', 'dark'] as const;
    const currentIndex = themes.indexOf(theme.value);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
};

const items = computed<DropdownMenuItem[][]>(() => [
    [
        {
            label: userName.value || 'Unknown User',
            avatar: {
                src: userImage.value || '',
            },
            type: 'label',
        },
        ...(userEmail.value ? [{
            label: userEmail.value,
            icon: 'i-lucide-mail',
            type: 'label' as const,
            class: 'text-gray-500 dark:text-gray-400 text-xs',
        }] : []),
    ],
    [
        {
            class: 'cursor-pointer',
            label: getThemeLabel(theme.value),
            icon: getThemeIcon(theme.value),
            onSelect: cycleTheme,
        },
        {
            class: 'cursor-pointer',
            label: 'Settings',
            icon: 'i-lucide-cog',
            to: '/settings',
        },
    ],
    [
        {
            class: 'cursor-pointer',
            label: 'Logout',
            icon: 'i-lucide-log-out',
            onSelect() {
                signOut();
            },
        },
    ],
]);
</script>

<template>
    <UDropdownMenu
        :items="items"
        size="md"
    >
        <UAvatar
            :src="userImage"
            :alt="userName"
            size="lg"
            class="cursor-pointer"
        />
    </UDropdownMenu>
</template>
