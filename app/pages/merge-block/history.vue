<script setup lang="ts">
import { h, resolveComponent } from 'vue';
import { getSortedRowModel, getFilteredRowModel } from '@tanstack/vue-table';
import type { TableColumn } from '@nuxt/ui';

// @ts-expect-error some bullshit
const table = useTemplateRef('table');

const UButton = resolveComponent('UButton');
const UBadge = resolveComponent('UBadge');

type PullRequest = {
    id: string;
    repo: string;
    source: string;
    target: string;
    author: string;
    openedAt: string;
    approver: string | null;
    approvedAt: string | null;
    status: 'unknown' | 'approved' | 'merged' | 'closed';
    title: string;
};

// Generate sample pull request data
const generatePullRequestData = (startId: number, count: number): PullRequest[] => {
    const repos = [
        'frontend-app', 'backend-api', 'mobile-app', 'data-pipeline', 'auth-service',
        'notification-service', 'payment-gateway', 'user-management', 'analytics-dashboard',
        'content-management', 'search-engine', 'recommendation-system',
    ];

    const authors = [
        'john.doe', 'jane.smith', 'mike.johnson', 'sarah.wilson', 'david.brown',
        'emily.davis', 'chris.miller', 'lisa.garcia', 'tom.anderson', 'anna.taylor',
    ];

    const approvers = [
        'tech.lead', 'senior.dev', 'architect', 'team.lead', 'principal.engineer',
        null, // Some PRs might not be approved yet
    ];

    const statuses: PullRequest['status'][] = ['unknown', 'approved', 'merged', 'closed'];

    const branches = [
        'feature/user-authentication', 'feature/payment-integration', 'bugfix/memory-leak',
        'feature/dark-mode', 'hotfix/security-patch', 'feature/api-optimization',
        'feature/mobile-responsive', 'bugfix/data-validation', 'feature/search-improvement',
    ];

    return Array.from({ length: count }, (_, i) => {
        const openedAt = new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000);
        const status = statuses[Math.floor(Math.random() * statuses.length)] || 'unknown';
        const approver = status === 'unknown' ? null : (approvers[Math.floor(Math.random() * approvers.length)] || null);
        const approvedAt = approver ? new Date(openedAt.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000) : null;

        return {
            id: String(startId - i),
            repo: repos[Math.floor(Math.random() * repos.length)] || 'unknown-repo',
            source: branches[Math.floor(Math.random() * branches.length)] || 'feature/unknown',
            target: 'main',
            author: authors[Math.floor(Math.random() * authors.length)] || 'unknown.user',
            openedAt: openedAt.toISOString(),
            approver,
            approvedAt: approvedAt?.toISOString() || null,
            status,
            title: `${branches[Math.floor(Math.random() * branches.length)]?.includes('bugfix') ? 'Fix' : 'Add'} ${['user authentication', 'payment processing', 'data validation', 'UI improvements', 'performance optimization'][Math.floor(Math.random() * 5)]}`,
        };
    });
};

const allData = ref<PullRequest[]>([
    ...generatePullRequestData(2500, 20),
    ...generatePullRequestData(2480, 50),
]);

const displayedData = ref<PullRequest[]>(allData.value.slice(0, 50));
const isLoading = ref(false);
const hasMore = ref(true);

// Global filter state
const globalFilter = ref('');

// Expanded rows state
const expanded = ref<Record<string, boolean>>({});

// Sorting state
const sorting = ref([{ id: 'openedAt', desc: true }]);

// Infinite scroll logic
const loadMore = async () => {
    if (isLoading.value || !hasMore.value) return;

    isLoading.value = true;

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const currentLength = displayedData.value.length;
    const nextBatch = allData.value.slice(currentLength, currentLength + 20);

    if (nextBatch.length === 0) {
        // Generate more data if we've reached the end
        const newData = generatePullRequestData(2500 - allData.value.length, 20);
        allData.value.push(...newData);
        displayedData.value.push(...newData);
    }
    else {
        displayedData.value.push(...nextBatch);
    }

    if (displayedData.value.length >= 200) {
        hasMore.value = false;
    }

    isLoading.value = false;
};

// Scroll event handler
const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement;
    const { scrollTop, scrollHeight, clientHeight } = target;

    if (scrollHeight - scrollTop <= clientHeight + 100) {
        loadMore();
    }
};

const getStatusColor = (status: PullRequest['status']) => {
    switch (status) {
        case 'approved': return 'success';
        case 'merged': return 'primary';
        case 'closed': return 'error';
        default: return 'neutral';
    }
};

const columns: TableColumn<PullRequest>[] = [
    {
        id: 'expand',
        cell: ({ row }) =>
            h(UButton, {
                'class': 'cursor-pointer',
                'color': 'neutral',
                'variant': 'ghost',
                'icon': 'i-lucide-chevron-down',
                'square': true,
                'aria-label': 'Expand',
                'ui': {
                    leadingIcon: [
                        'transition-transform',
                        row.getIsExpanded() ? 'duration-200 rotate-180' : '',
                    ],
                },
                'onClick': () => row.toggleExpanded(),
            }),
        enableSorting: false,
    },
    {
        accessorKey: 'id',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'PR #',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => h('span', { class: 'font-mono text-sm' }, `#${row.getValue('id')}`),
        enableSorting: true,
    },
    {
        accessorKey: 'repo',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Repository',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => h('span', { class: 'font-medium' }, row.getValue('repo')),
        enableSorting: true,
    },
    {
        accessorKey: 'source',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Source',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => h('span', { class: 'font-mono text-xs text-gray-600 dark:text-gray-400' }, row.getValue('source')),
        enableSorting: true,
    },
    {
        accessorKey: 'target',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Target',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => h('span', { class: 'font-mono text-xs font-medium' }, row.getValue('target')),
        enableSorting: true,
    },
    {
        accessorKey: 'author',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Author',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => h('span', { class: 'font-medium' }, row.getValue('author')),
        enableSorting: true,
    },
    {
        accessorKey: 'openedAt',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Opened',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => {
            return new Date(row.getValue('openedAt')).toLocaleString('en-US', {
                day: 'numeric',
                month: 'short',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false,
            });
        },
        enableSorting: true,
    },
    {
        accessorKey: 'approver',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Approver',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => {
            const approver = row.getValue('approver') as string | null;
            return approver
                ? h('span', { class: 'font-medium' }, approver)
                : h('span', { class: 'text-gray-400 dark:text-gray-600 italic' }, 'Pending');
        },
        enableSorting: true,
    },
    {
        accessorKey: 'approvedAt',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Approved',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => {
            const approvedAt = row.getValue('approvedAt') as string | null;
            return approvedAt
                ? new Date(approvedAt).toLocaleString('en-US', {
                        day: 'numeric',
                        month: 'short',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false,
                    })
                : h('span', { class: 'text-gray-400 dark:text-gray-600 italic' }, 'Not approved');
        },
        enableSorting: true,
    },
    {
        accessorKey: 'status',
        header: ({ column }) => {
            const isSorted = column.getIsSorted();
            return h(UButton, {
                color: 'neutral',
                variant: 'ghost',
                label: 'Status',
                icon: isSorted
                    ? isSorted === 'asc'
                        ? 'i-lucide-arrow-up-narrow-wide'
                        : 'i-lucide-arrow-down-wide-narrow'
                    : 'i-lucide-arrow-up-down',
                class: 'cursor-pointer -mx-2.5 text-xs',
                onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
            });
        },
        cell: ({ row }) => {
            const status = row.getValue('status') as PullRequest['status'];
            return h(UBadge, {
                color: getStatusColor(status),
                variant: 'subtle',
                label: status.charAt(0).toUpperCase() + status.slice(1),
            });
        },
        enableSorting: true,
    },
];
</script>

<template>
    <div class="flex flex-col h-full overflow-hidden">
        <div class="p-6 flex-shrink-0">
            <div class="max-w-7xl mx-auto">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <!-- Mobile breadcrumb: two lines -->
                    <div class="lg:hidden">
                        <div class="text-sm font-bold text-gray-400 dark:text-gray-400">
                            Merge Block
                        </div>
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                            History
                        </div>
                    </div>

                    <!-- Desktop breadcrumb: single line -->
                    <h1 class="hidden lg:block text-3xl font-bold text-gray-900 dark:text-white">
                        <span class="text-gray-400 dark:text-gray-400">Merge Block /</span> History
                    </h1>

                    <UInput
                        v-model="globalFilter"
                        placeholder="Search pull requests..."
                        icon="i-lucide-search"
                        size="md"
                        class="w-full lg:w-auto"
                    />
                </div>
            </div>
        </div>

        <div class="flex-1 px-6 overflow-hidden">
            <div class="max-w-7xl mx-auto h-[calc(100%-24px)]">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 h-full flex flex-col">
                    <div
                        class="flex-1 overflow-auto"
                        @scroll="handleScroll"
                    >
                        <UTable
                            ref="table"
                            v-model:expanded="expanded"
                            v-model:sorting="sorting"
                            v-model:global-filter="globalFilter"
                            :data="displayedData"
                            :columns="columns"
                            :sorting-options="{
                                getSortedRowModel: getSortedRowModel,
                            }"
                            :global-filter-options="{
                                getFilteredRowModel: getFilteredRowModel,
                            }"
                            :loading="isLoading"
                            sticky="header"
                            class="h-full"
                            :ui="{
                                tr: 'data-[expanded=true]:bg-elevated/50',
                                thead: 'bg-gray-100/50 dark:bg-gray-900/50',
                                root: 'h-full',
                            }"
                        >
                            <template #expanded="{ row }">
                                <div class="p-4 bg-gray-50 dark:bg-gray-900/50">
                                    <h4 class="font-semibold mb-2">
                                        Pull Request Details
                                    </h4>
                                    <div class="grid grid-cols-2 gap-4 text-sm mb-4">
                                        <div>
                                            <span class="font-medium">PR ID:</span>
                                            <span class="ml-2">#{{ row.original.id }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Repository:</span>
                                            <span class="ml-2">{{ row.original.repo }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Source Branch:</span>
                                            <span class="ml-2 font-mono text-xs">{{ row.original.source }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Target Branch:</span>
                                            <span class="ml-2 font-mono text-xs">{{ row.original.target }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Author:</span>
                                            <span class="ml-2">{{ row.original.author }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Status:</span>
                                            <UBadge
                                                :color="getStatusColor(row.original.status)"
                                                variant="subtle"
                                                :label="row.original.status.charAt(0).toUpperCase() + row.original.status.slice(1)"
                                                class="ml-2"
                                            />
                                        </div>
                                        <div>
                                            <span class="font-medium">Opened:</span>
                                            <span class="ml-2">{{ new Date(row.original.openedAt).toLocaleString() }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium">Approver:</span>
                                            <span class="ml-2">{{ row.original.approver || 'Pending approval' }}</span>
                                        </div>
                                        <div v-if="row.original.approvedAt">
                                            <span class="font-medium">Approved:</span>
                                            <span class="ml-2">{{ new Date(row.original.approvedAt).toLocaleString() }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="font-medium">Title:</span>
                                        <p class="mt-1 text-gray-700 dark:text-gray-300">
                                            {{ row.original.title }}
                                        </p>
                                    </div>
                                    <div class="mt-4">
                                        <pre class="text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-x-auto">{{ JSON.stringify(row.original, null, 2) }}</pre>
                                    </div>
                                </div>
                            </template>
                        </UTable>
                    </div>

                    <!-- Loading indicator for infinite scroll -->
                    <div
                        v-if="isLoading"
                        class="p-4 text-center border-t border-gray-200 dark:border-gray-700"
                    >
                        <div class="flex items-center justify-center space-x-2">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" />
                            <span class="text-sm text-gray-600 dark:text-gray-400">Loading more pull requests...</span>
                        </div>
                    </div>

                    <!-- End of data indicator -->
                    <div
                        v-if="!hasMore && !isLoading"
                        class="p-4 text-center border-t border-gray-200 dark:border-gray-700"
                    >
                        <span class="text-sm text-gray-500 dark:text-gray-500">
                            No more pull requests to load
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
